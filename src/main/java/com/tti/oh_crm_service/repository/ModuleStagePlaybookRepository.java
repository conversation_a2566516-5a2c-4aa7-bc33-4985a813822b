package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.ModuleStagePlaybook;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ModuleStagePlaybookRepository extends JpaRepository<ModuleStagePlaybook, Long> {
    List<ModuleStagePlaybook> findByGroupedModulePlaybookProcessId(Long groupedModulePlaybookProcessId);

    Optional<ModuleStagePlaybook> findByModuleCodeAndModuleStageValue(String moduleCode, String moduleStageValue);

    @Query("SELECT msp FROM ModuleStagePlaybook msp LEFT JOIN FETCH msp.playbookTools WHERE msp.id = :id")
    ModuleStagePlaybook findByIdWithPlaybookTools(@Param("id") Long id);
}
