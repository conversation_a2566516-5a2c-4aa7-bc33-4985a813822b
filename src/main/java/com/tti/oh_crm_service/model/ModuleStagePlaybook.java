package com.tti.oh_crm_service.model;

import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

@Entity
@Table(name = "module_stage_playbooks",
       uniqueConstraints = @UniqueConstraint(columnNames = {"module_code", "module_stage_value"}))
@Data
public class ModuleStagePlaybook extends AuditableBaseEntity {
    // Now only available for LEAD and OPPORTUNITY
    @Column(name = "module_code", nullable = false)
    private String moduleCode;
    @Column(name = "module_stage_value", nullable = false)
    private String moduleStageValue;
    // longtext for rich-text editor data
    @Column(name = "guide_for_success_content", columnDefinition = "LONGTEXT")
    private String guideForSuccessContent;
    @ManyToOne(fetch = FetchType.LAZY)
    private GroupedModulePlaybookProcess groupedModulePlaybookProcess;

    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
        name = "module_stage_playbook_tools",
        joinColumns = @JoinColumn(name = "module_stage_playbook_id"),
        inverseJoinColumns = @JoinColumn(name = "playbook_tool_id")
    )
    private List<PlaybookTool> playbookTools;


}
